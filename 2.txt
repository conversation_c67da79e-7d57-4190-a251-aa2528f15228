"""
Edge必应自动搜索赚积分
使用Edge浏览器打开必应 https://rewards.bing.com/ F12抓取Cookie即可  正确的CK格式是以MUID=xxxxxxx开头的

Cookie配置方式:
配置文件：cookie.txt  单账号模式（使用第一行有效Cookie）

如果执行的发现积分不增长，且脚本上显示的积分跟实际不符，很有可能不是同一个账号的cookie，建议重新抓取。
玄学报错目前无解，凑合用吧！
"""

# ============= 配置区域 =============
# 钉钉通知配置
DINGDING_CONFIG = {
    'webhook': 'https://oapi.dingtalk.com/robot/send?access_token=fe7d9235f3724127f5de5cf209c8eaf23d1544b51c90ebe8ea356a273d8511f6',
    'secret': 'SECe8cd72b73a2c715008336c5a5aa1f66d951d6079a61ac08950df2590250bf144'
}

# 搜索相关配置
SEARCH_CONFIG = {
    'pc_max_per_hour': 4,  # PC端每小时最大搜索次数
    'mobile_max_per_hour': 3,  # 移动端每小时最大搜索次数
    'pc_max_daily': 90,  # PC端每日最大搜索次数
    'mobile_max_daily': 60,  # 移动端每日最大搜索次数
    'working_hours': {
        'start': 6,  # 开始工作时间
        'end': 21    # 结束工作时间
    }
}

# Cookie验证配置
VALIDATION_CONFIG = {
    'max_attempts': 3,  # 最大验证次数
    'retry_wait_min': 5,  # 重试等待最小时间(秒)
    'retry_wait_max': 10  # 重试等待最大时间(秒)
}

# 搜索间隔配置
INTERVAL_CONFIG = {
    'pc_min': 5,  # PC端最小搜索间隔(秒)
    'pc_max': 12,  # PC端最大搜索间隔(秒)
    'mobile_min': 8,  # 移动端最小搜索间隔(秒)
    'mobile_max': 15,  # 移动端最大搜索间隔(秒)
    'device_switch_min': 15,  # 设备切换最小间隔(秒)
    'device_switch_max': 30,  # 设备切换最大间隔(秒)
    'mobile_rest_interval': 3,  # 移动端每几次搜索后休息
    'mobile_rest_min': 20,  # 移动端休息最小时间(秒)
    'mobile_rest_max': 30   # 移动端休息最大时间(秒)
}

# 奖励任务配置
REWARD_CONFIG = {
    'execution_times': [10, 13, 20],  # 执行奖励任务的时间点
    'min_wait': 3,  # 任务间最小等待时间(秒)
    'max_wait': 6   # 任务间最大等待时间(秒)
}

# 删除账号切换配置（单账号模式不需要）

# ============= 导入区域 =============
import requests
import os
import time
import random
import json
import datetime
import re
import urllib.parse
import html  # 用于HTML解码
# 删除argparse导入（不再需要命令行参数）
# 删除BeautifulSoup导入（未使用）
import hmac
import hashlib
import base64
from urllib.parse import quote_plus

try:
    from colorama import init, Fore, Back, Style
    init()
    COLORED_OUTPUT = True
except ImportError:
    COLORED_OUTPUT = False

# 删除热搜API（使用智能关键词生成器替代）

# 优化的搜索词库
SEARCH_WORDS = {
    'news': ["今日新闻", "国际新闻", "财经新闻", "体育新闻", "科技新闻", "娱乐新闻"],
    'life': ["美食菜谱", "旅游攻略", "健康知识", "装修设计", "家居生活", "购物指南"],
    'entertainment': ["电影推荐", "音乐排行榜", "游戏攻略", "综艺节目", "明星动态", "热门剧集"],
    'technology': ["科技资讯", "手机评测", "电脑配置", "数码产品", "人工智能", "新能源汽车"],
    'education': ["考研资料", "英语学习", "编程教程", "考试技巧", "在线课程", "职业培训"],
    'business': ["创业项目", "理财知识", "投资技巧", "电商平台", "股市分析", "经济趋势"],
    'sports': ["NBA比赛", "足球赛事", "奥运会", "健身计划", "运动装备", "体育明星"],
    'culture': ["历史故事", "文学作品", "艺术展览", "传统文化", "博物馆", "书籍推荐"],
    'weather': ["天气预报", "气候变化", "自然灾害", "季节养生", "户外活动", "环境保护"]
}

# 动态关键词生成器
class SmartKeywordGenerator:
    def __init__(self):
        self.used_keywords = set()
        self.keyword_patterns = [
            "如何{}", "{}教程", "{}推荐", "最新{}", "{}排行榜",
            "{}技巧", "{}方法", "{}攻略", "{}指南", "{}大全"
        ]
        self.time_based_keywords = self._generate_time_keywords()

    def _generate_time_keywords(self):
        """生成基于时间的关键词"""
        import datetime
        now = datetime.datetime.now()
        keywords = []

        # 季节相关
        season_map = {1: "冬季", 2: "冬季", 3: "春季", 4: "春季", 5: "春季",
                     6: "夏季", 7: "夏季", 8: "夏季", 9: "秋季", 10: "秋季", 11: "秋季", 12: "冬季"}
        current_season = season_map[now.month]
        keywords.extend([f"{current_season}养生", f"{current_season}旅游", f"{current_season}美食"])

        # 节日相关
        if now.month == 1: keywords.extend(["新年计划", "春节习俗"])
        elif now.month == 2: keywords.extend(["情人节礼物", "元宵节"])
        elif now.month == 3: keywords.extend(["植树节", "妇女节"])
        elif now.month == 12: keywords.extend(["圣诞节", "年终总结"])

        return keywords

    def get_smart_keyword(self):
        """智能生成搜索关键词"""
        # 30%概率使用时间相关关键词
        if random.random() < 0.3 and self.time_based_keywords:
            keyword = random.choice(self.time_based_keywords)
        else:
            # 随机选择类别和关键词
            category = random.choice(list(SEARCH_WORDS.keys()))
            base_keyword = random.choice(SEARCH_WORDS[category])

            # 30%概率添加修饰词
            if random.random() < 0.3:
                pattern = random.choice(self.keyword_patterns)
                keyword = pattern.format(base_keyword)
            else:
                keyword = base_keyword

        # 避免重复
        if keyword in self.used_keywords:
            return self.get_smart_keyword()

        self.used_keywords.add(keyword)
        if len(self.used_keywords) > 100:  # 限制记忆大小
            self.used_keywords.clear()

        return keyword

# 全局关键词生成器
keyword_generator = SmartKeywordGenerator()
# 热门网站域名
DOMAINS = [
    # 综合门户
    "zhihu.com", "bilibili.com", "douyin.com", "weibo.com",
    # 新闻资讯
    "163.com", "qq.com", "sina.com.cn", "sohu.com",
    # 购物平台
    "taobao.com", "jd.com", "pinduoduo.com", "tmall.com",
    # 视频网站
    "youku.com", "iqiyi.com", "v.qq.com", "mgtv.com",
    # 生活服务
    "dianping.com", "meituan.com", "ctrip.com", "xiachufang.com",
    # 教育网站
    "csdn.net", "juejin.cn", "zhihu.com", "jianshu.com"
]
# 浏览器 User-Agent 配置
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/108.0.1462.54",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.78",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/111.0.1661.44",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.39",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/113.0.1774.35"
]
MOBILE_USER_AGENTS = [
    "Mozilla/5.0 (Linux; Android 12; Mi 10 Pro Build/SKQ1.220303.001; ) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/99.0.4844.88 Mobile Safari/537.36 BingSapphire/23.5.2110003534",
    "Mozilla/5.0 (Linux; Android 13; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/112.0.1722.46",
    "Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/112.0.1722.46",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Mobile/15E148 Safari/604.1 BingSapphire/23.5.2110003534",
    "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/112.0.1722.46",
    "Mozilla/5.0 (Linux; Android 13; IN2023) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/112.0.1722.46"
]
# 直接设置固定值
searchThreshold = 100  # 固定为100%，即搜索到达上限
# 生成浏览器指纹信息
def get_browser_info():
    edge_versions = ['108.0.1462.54', '109.0.1518.78', '110.0.1587.41', 
                    '111.0.1661.44', '112.0.1722.39', '113.0.1774.35']
    chrome_versions = ['*********', '*********', '110.0.0.0', 
                     '*********', '*********', '*********']
    
    edge_ver = random.choice(edge_versions)
    chrome_ver = random.choice(chrome_versions)
    
    return {
        'edge_version': edge_ver,
        'chrome_version': chrome_ver,
        'sec_ch_ua': f'"Not A(Brand";v="{chrome_ver.split(".")[0]}", "Chromium";v="{edge_ver.split(".")[0]}"',
        'sec_ch_ua_full_version': edge_ver,
        'sec_ch_ua_full_version_list': f'"Not A(Brand";v="8.0.0.0", "Chromium";v="{chrome_ver}", "Microsoft Edge";v="{edge_ver}"'
    }
# 完整请求头
def get_headers(bingCK, isPc=True):
    browser_info = get_browser_info()
    printLog('Cookie信息', f'当前使用的Cookie: {bingCK}')
    base_headers = {
        "cookie": bingCK,
        "User-Agent": f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser_info['chrome_version']} Safari/537.36 Edg/{browser_info['edge_version']}",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "max-age=0",
        "Connection": "keep-alive",
        "DNT": "1",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-User": "?1",
        "Sec-Fetch-Dest": "document",
        "Sec-Ch-Ua": browser_info['sec_ch_ua'],
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Full-Version": browser_info['sec_ch_ua_full_version'],
        "Sec-Ch-Ua-Arch": "x86",
        "Sec-Ch-Ua-Platform": "Windows",
        "Sec-Ch-Ua-Platform-Version": "15.0.0",
        "Sec-Ch-Ua-Model": "",
        "Sec-Ch-Ua-Bitness": "64",
        "Sec-Ch-Ua-Full-Version-List": browser_info['sec_ch_ua_full_version_list'],
        "X-Edge-Shopping-Flag": "1",
        "Priority": "u=0, i"
    }
    
    if not isPc:
        # 修改移动端请求头
        base_headers.update({
            "User-Agent": "Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/112.0.1722.46",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "DNT": "1",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-User": "?1",
            "Sec-Fetch-Dest": "document",
            "Sec-Ch-Ua-Mobile": "?1",
            "Sec-Ch-Ua-Platform": "Android",
            "Sec-Ch-Ua-Platform-Version": "13.0.0",
            "X-Requested-With": "com.microsoft.emmx",
            "X-Edge-Shopping-Flag": "1",
            # 新增必要的头部
            "X-Edge-UA": "Android",
            "X-MSEdge-ExternalExpType": "JointCoord",
            "X-MSEdge-ExternalExp": "001,h2c",
            "X-MSEdge-DeviceFamily": "Android"
        })
    
    return base_headers
# 生成随机搜索词
def get_random_search():
    """获取随机搜索词"""
    return search_pool.get_search_word()

# 删除热搜获取函数（已被智能关键词生成器替代）

old_Balance = 0
bingDetectionStop = os.getenv("bingDetectionStop")
#获取用户信息
def getDashboard(bingCK):
    try:
        url = 'https://rewards.bing.com/pointsbreakdown'
        headers = {
            "cookie": bingCK,
            "User-Agent": random.choice(USER_AGENTS),  # 使用随机PC UA
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-User": "?1",
            "Sec-Fetch-Dest": "document",
            "sec-ch-ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Microsoft Edge\";v=\"120\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\""
        }
        printLog('调试', f'请求URL: {url}')
        printLog('调试', '发送请求...')
        
        with requests.get(url, headers=headers, timeout=10) as resp:
            resp.encoding = 'utf-8'  # 设置响应编码
            resp.raise_for_status()
            text = resp.text
            printLog('调试', f'响应状态码: {resp.status_code}')
            printLog('调试', f'响应内容前200字符: {text[:200]}')
            
            if 'var dashboard = ' not in text:
                printLog('调试', 'Dashboard数据未找到')
                return None
                
            str_start = 'var dashboard = '
            str_end = '{}};'
            retStr = find(text, str_start, str_end) + "{}}"
            
            # 验证JSON格式
            try:
                json.loads(retStr)
                return retStr
            except json.JSONDecodeError:
                printLog('调试', 'Dashboard数据不是有效的JSON格式')
                return None
                
    except requests.RequestException as e:
        printLog('调试-请求异常', str(e))
        return None
    except Exception as e:
        printLog('调试-其他异常', str(e))
        return None

#获取当前积分
def getBalance(bingCK):
    try:
        url = 'https://rewards.bing.com/pointsbreakdown'
        headers = {
            "cookie": bingCK,
            "User-Agent": random.choice(USER_AGENTS),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-User": "?1",
            "Sec-Fetch-Dest": "document"
        }
        
        resp = requests.get(url, headers=headers, timeout=10)
        resp.encoding = 'utf-8'
        
        text = resp.text
        
        # 提取基本积分信息
        points_match = re.search(r'"availablePoints":(\d+),', text)
        balance_match = re.search(r'"balance":(\d+)', text)
        
        # 提取用户信息
        name_match = re.search(r'displayName:\s*"([^"]+)"', text)
        email_match = re.search(r'email:\s*"([^"]+)"', text)
        
        # 提取搜索进度信息
        pc_search_info = re.search(r'"pcSearch":\[(.*?)\]', text)
        mobile_search_info = re.search(r'"mobileSearch":\[(.*?)\]', text)
        
        result = {}
        
        # 基本积分
        if points_match:
            result['points'] = points_match.group(1)
        elif balance_match:
            result['points'] = balance_match.group(1)
        else:
            result['points'] = None
            
        # 用户信息
        if name_match:
            result['name'] = html.unescape(name_match.group(1))
            if not hasattr(getBalance, 'name_printed'):
                printLog('用户名', result['name'])
                setattr(getBalance, 'name_printed', True)
                
        if email_match:
            result['email'] = email_match.group(1)
            if not hasattr(getBalance, 'email_printed'):
                printLog('邮箱', result['email'])
                setattr(getBalance, 'email_printed', True)
            
        # PC搜索信息
        if pc_search_info:
            pc_data = pc_search_info.group(1)
            pc_progress = re.search(r'"pointProgress":(\d+)', pc_data)
            pc_max = re.search(r'"pointProgressMax":(\d+)', pc_data)
            if pc_progress and pc_max:
                result['pc_progress'] = pc_progress.group(1)
                result['pc_max'] = pc_max.group(1)
                
        # 移动端搜索信息
        if mobile_search_info:
            mobile_data = mobile_search_info.group(1)
            mobile_progress = re.search(r'"pointProgress":(\d+)', mobile_data)
            mobile_max = re.search(r'"pointProgressMax":(\d+)', mobile_data)
            if mobile_progress and mobile_max:
                result['mobile_progress'] = mobile_progress.group(1)
                result['mobile_max'] = mobile_max.group(1)
        
        # 输出详细信息
        if result.get('points'):
            printLog('当前积分', result['points'])
        if result.get('pc_progress') and result.get('pc_max'):
            printLog('PC搜索进度', f"{result['pc_progress']}/{result['pc_max']}")
        if result.get('mobile_progress') and result.get('mobile_max'):
            printLog('移动搜索进度', f"{result['mobile_progress']}/{result['mobile_max']}")
            
        return result
                
    except Exception as e:
        printLog('积分异常', str(e))
        return None

def simulate_human_behavior():
    """模拟人类行为特征"""
    class HumanBehavior:
        def __init__(self):
            self.typing_speed = random.uniform(0.1, 0.3)  # 每个字符的基础输入速度
            self.error_rate = 0.05  # 输入错误率
            self.correction_chance = 0.8  # 错误更正概率
            self.pause_chance = 0.15  # 停顿概率
            self.scroll_patterns = ['smooth', 'jump', 'read']  # 滚动模式
            
        def simulate_typing(self, text):
            """模拟人类打字行为"""
            typed_text = ""
            for char in text:
                # 模拟打字错误
                if random.random() < self.error_rate:
                    wrong_char = random.choice('abcdefghijklmnopqrstuvwxyz')
                    typed_text += wrong_char
                    time.sleep(self.typing_speed)
                    
                    # 模拟错误更正
                    if random.random() < self.correction_chance:
                        time.sleep(0.5)  # 察觉错误的反应时间
                        typed_text = typed_text[:-1]  # 删除错误字符
                        time.sleep(0.3)  # 删除后短暂停顿
                        
                typed_text += char
                
                # 基础打字延迟
                delay = self.typing_speed * (1 + random.uniform(-0.2, 0.2))
                time.sleep(delay)
                
                # 随机停顿
                if random.random() < self.pause_chance:
                    time.sleep(random.uniform(0.5, 2.0))
                    
            return typed_text
            
        def simulate_mouse_movement(self):
            """模拟鼠标移动"""
            movements = [
                ('移动到搜索框', random.uniform(0.3, 0.8)),
                ('点击搜索框', random.uniform(0.1, 0.3)),
                ('偶尔移出搜索框', random.uniform(0.2, 0.5))
            ]
            
            for action, delay in movements:
                if action == '偶尔移出搜索框' and random.random() < 0.3:
                    printLog('模拟操作', f'{action}')
                    time.sleep(delay)
                else:
                    printLog('模拟操作', f'{action}')
                    time.sleep(delay)
                    
        def simulate_scrolling(self, content_height):
            """模拟页面滚动行为"""
            scroll_pattern = random.choice(self.scroll_patterns)
            
            if scroll_pattern == 'smooth':
                # 平滑滚动
                current_position = 0
                while current_position < content_height:
                    scroll_amount = random.randint(100, 300)
                    current_position += scroll_amount
                    printLog('浏览行为', f'平滑滚动至 {current_position}px')
                    time.sleep(random.uniform(0.5, 1.5))
                    
            elif scroll_pattern == 'jump':
                # 跳跃式滚动
                positions = sorted([random.randint(0, content_height) for _ in range(3)])
                for pos in positions:
                    printLog('浏览行为', f'跳转至 {pos}px')
                    time.sleep(random.uniform(1, 2))
                    
            else:  # read pattern
                # 阅读模式
                sections = [(0, 500), (500, 1000), (1000, 1500)]
                for start, end in sections:
                    printLog('浏览行为', f'仔细阅读 {start}-{end}px 区域')
                    time.sleep(random.uniform(2, 4))
                    
        def simulate_result_interaction(self):
            """模拟搜索结果交互"""
            if random.random() < 0.4:  # 40%概率与结果交互
                interactions = [
                    ('hover', '悬停在搜索结果上'),
                    ('click', '点击搜索结果'),
                    ('back', '返回搜索页面')
                ]
                
                interaction = random.choice(interactions)
                printLog('交互行为', interaction[1])
                time.sleep(random.uniform(1, 3))
                
                if interaction[0] == 'click':
                    # 模拟查看内容后返回
                    time.sleep(random.uniform(3, 8))
                    printLog('交互行为', '返回搜索页面')
                    
    return HumanBehavior()

class SearchBehaviorAnalyzer:
    def __init__(self):
        self.search_history = []
        self.interaction_patterns = []
        self.timing_patterns = []
        self.success_rate = 0
        self.detection_score = 0
        
    def analyze_search(self, search_word, timing, success):
        """分析搜索行为"""
        self.search_history.append({
            'word': search_word,
            'time': timing,
            'success': success
        })
        
        # 更新成功率
        total_searches = len(self.search_history)
        successful_searches = sum(1 for s in self.search_history if s['success'])
        self.success_rate = successful_searches / total_searches if total_searches > 0 else 0
        
        # 分析时间模式
        if len(self.search_history) >= 2:
            time_diff = self.search_history[-1]['time'] - self.search_history[-2]['time']
            self.timing_patterns.append(time_diff)
            
        # 计算检测分数
        self._calculate_detection_score()
        
        return self.get_risk_level()
        
    def _calculate_detection_score(self):
        """计算检测风险分数"""
        score = 0
        
        # 检查搜索频率
        if len(self.timing_patterns) >= 3:
            avg_interval = sum(self.timing_patterns[-3:]) / 3
            if avg_interval < 5:  # 平均间隔小于5秒
                score += 30
            elif avg_interval < 10:  # 平均间隔小于10秒
                score += 15
                
        # 检查成功率
        if self.success_rate < 0.5:  # 成功率低于50%
            score += 25
        elif self.success_rate < 0.7:  # 成功率低于70%
            score += 15
            
        # 检查搜索词模式
        if len(self.search_history) >= 3:
            recent_words = [s['word'] for s in self.search_history[-3:]]
            if len(set(recent_words)) == 1:  # 完全相同的搜索词
                score += 40
            elif len(set(recent_words)) == 2:  # 仅有2个不同的搜索词
                score += 20
                
        self.detection_score = score
        
    def get_risk_level(self):
        """获取风险等级"""
        if self.detection_score >= 70:
            return 'high'
        elif self.detection_score >= 40:
            return 'medium'
        return 'low'
        
    def should_adjust_behavior(self):
        """判断是否需要调整行为"""
        return self.detection_score >= 40
        
    def get_recommended_delay(self):
        """获取建议的延迟时间"""
        if self.detection_score >= 70:
            return random.uniform(15, 30)  # 高风险情况
        elif self.detection_score >= 40:
            return random.uniform(10, 20)  # 中等风险情况
        return random.uniform(5, 15)  # 低风险情况

class SearchWordPool:
    def __init__(self):
        # 将字典形式的SEARCH_WORDS转换为列表
        all_words = []
        for category, words in SEARCH_WORDS.items():
            all_words.extend(words)
        self.search_words = all_words.copy()
        self.used_words = set()
        self.related_words = {}
        self.search_pattern = 'random'
        self.compound_patterns = [
            lambda w: f"如何{w}",
            lambda w: f"{w}教程",
            lambda w: f"{w}推荐",
            lambda w: f"最新{w}",
            lambda w: f"{w}排行榜",
            lambda w: f"{w}技巧",
            lambda w: f"{w}方法",
            lambda w: f"{w}攻略"
        ]
        
    def add_related_words(self, word, related):
        self.related_words[word] = related
        
    def generate_compound_word(self, base_word):
        pattern = random.choice(self.compound_patterns)
        return pattern(base_word)
        
    def get_search_word(self):
        if not self.search_words:
            # 重新生成搜索词列表
            all_words = []
            for category, words in SEARCH_WORDS.items():
                all_words.extend(words)
            self.search_words = all_words.copy()
            self.used_words.clear()
            
        if self.search_pattern == 'random':
            word = random.choice(self.search_words)
        elif self.search_pattern == 'sequential':
            word = self.search_words[0]
        else:
            word = random.choice(self.search_words)
            
        self.search_words.remove(word)
        self.used_words.add(word)
        
        # 20%概率生成复合词
        if random.random() < 0.2:
            word = self.generate_compound_word(word)
            
        # 如果有相关词，30%概率使用相关词
        if word in self.related_words and random.random() < 0.3:
            related = self.related_words[word]
            if related:
                word = random.choice(related)
                
        return word
        
    def switch_search_pattern(self):
        patterns = ['random', 'sequential']
        self.search_pattern = random.choice([p for p in patterns if p != self.search_pattern])

# 创建全局搜索词池实例
search_pool = SearchWordPool()

class SmartSearchManager:
    def __init__(self):
        self.behavior_analyzer = SearchBehaviorAnalyzer()
        self.search_pool = SearchWordPool()
        self.current_mode = 'normal'
        self.consecutive_failures = 0
        self.last_search_time = None
        self.last_search_word = None  # 添加 last_search_word 属性
        
    def prepare_search(self, is_pc):
        """准备搜索参数"""
        current_time = time.time()
        
        # 根据之前的分析结果调整行为
        if self.behavior_analyzer.should_adjust_behavior():
            self._adjust_search_behavior()
            
        # 获取搜索词
        search_word = self._get_smart_search_word()
        self.last_search_word = search_word  # 更新 last_search_word
        
        # 计算延迟时间
        delay = self._calculate_delay(is_pc)
        
        # 更新最后搜索时间
        self.last_search_time = current_time
        
        return search_word, delay
        
    def _adjust_search_behavior(self):
        """调整搜索行为"""
        if self.current_mode == 'normal':
            self.current_mode = 'cautious'
            self.search_pool.switch_search_pattern()  # 切换搜索模式
        elif self.current_mode == 'cautious':
            self.current_mode = 'conservative'
            # 在保守模式下使用更安全的搜索模式
            self.search_pool.search_pattern = 'sequential'
            
    def _get_smart_search_word(self):
        """智能选择搜索词"""
        if self.current_mode == 'conservative':
            # 在保守模式下使用更安全的搜索词
            return self.search_pool.get_search_word()
        elif self.current_mode == 'cautious':
            # 在谨慎模式下增加相关词
            base_word = self.search_pool.get_search_word()
            self.search_pool.add_related_words(base_word, [
                f"{base_word}教程",
                f"{base_word}推荐",
                f"{base_word}排行"
            ])
            return base_word
        else:
            # 正常模式
            return self.search_pool.get_search_word()
            
    def _calculate_delay(self, is_pc):
        """计算下次搜索延迟"""
        base_delay = self.behavior_analyzer.get_recommended_delay()
        
        if is_pc:
            delay_range = (5, 12)
        else:
            delay_range = (8, 15)
            
        # 根据模式调整延迟
        if self.current_mode == 'conservative':
            delay_multiplier = 2.0
        elif self.current_mode == 'cautious':
            delay_multiplier = 1.5
        else:
            delay_multiplier = 1.0
            
        return min(max(base_delay * delay_multiplier, delay_range[0]), delay_range[1])

    def calculate_delay(self, is_pc):
        """公共方法：计算搜索延迟"""
        return self._calculate_delay(is_pc)

    def update_search_result(self, success):
        """更新搜索结果"""
        if success:
            self.consecutive_failures = 0
            if self.current_mode != 'normal':
                # 成功后逐步恢复正常模式
                if self.current_mode == 'conservative':
                    self.current_mode = 'cautious'
                elif self.current_mode == 'cautious':
                    self.current_mode = 'normal'
        else:
            self.consecutive_failures += 1
            if self.consecutive_failures >= 3:
                self._adjust_search_behavior()
                
        # 分析搜索行为
        risk_level = self.behavior_analyzer.analyze_search(
            self.last_search_word,  # 现在可以安全使用 last_search_word
            time.time(),
            success
        )
        
        return risk_level

# 创建智能搜索管理器实例
smart_search = SmartSearchManager()

def bing_rewards(q_str, bingCK, isPc=True):
    try:
        # 使用智能关键词生成器
        search_word = keyword_generator.get_smart_keyword()

        # 根据搜索类型过滤Cookie
        filter_type = 'pc' if isPc else 'mobile'
        filtered_cookie = filter_cookies(bingCK, filter_type)

        # 获取请求配置
        headers, params = get_request_config(search_word, isPc)
        headers['cookie'] = filtered_cookie

        # 智能延迟
        delay = smart_search.calculate_delay(isPc)
        time.sleep(delay)

        # 执行搜索请求
        search_url = 'https://cn.bing.com/search'
        response = make_request(
            url=search_url,
            method='GET',
            headers=headers,
            params=params
        )

        if response:
            success = True
            risk_level = smart_search.update_search_result(success)

            if risk_level == 'high':
                printLog('风险警告', '检测到高风险行为，暂停搜索')
                time.sleep(random.uniform(300, 600))
            elif risk_level == 'medium':
                printLog('风险提示', '检测到中等风险，调整搜索行为')

            return '成功'
        else:
            smart_search.update_search_result(False)
            return '失败'

    except Exception as e:
        printLog('错误', f'搜索执行出错: {str(e)}')
        return f'执行异常: {str(e)}'

# 工具函数
def extract_text(text, start_marker, end_marker):
    """提取文本片段"""
    try:
        start_index = text.find(start_marker) + len(start_marker)
        end_index = text.find(end_marker)
        return text[start_index:end_index].strip()
    except:
        return ""

# 简化 DingDing 类,只保留基本通知功能
class DingDing:
    def __init__(self, webhook):
        self.webhook = webhook
        self.secret = None

    def set_secret(self, secret):
        self.secret = secret

    def get_sign(self):
        timestamp = str(round(time.time() * 1000))
        secret_enc = self.secret.encode('utf-8')
        string_to_sign = '{}\n{}'.format(timestamp, self.secret)
        string_to_sign_enc = string_to_sign.encode('utf-8')
        hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
        sign = quote_plus(base64.b64encode(hmac_code))
        return timestamp, sign

    def send_text(self, text, at_mobiles=None, at_all=False):
        if self.secret is not None:
            timestamp, sign = self.get_sign()
            webhook = f"{self.webhook}&timestamp={timestamp}&sign={sign}"
        else:
            webhook = self.webhook

        data = {"msgtype": "text", "text": {"content": text}}
        if at_mobiles or at_all:
            data["at"] = {"atMobiles": at_mobiles or [], "isAtAll": at_all}

        headers = {"Content-Type": "application/json"}
        response = requests.post(webhook, json=data, headers=headers)
        return response.json()

# 初始化钉钉机器人
ding = DingDing(DINGDING_CONFIG['webhook'])
ding.set_secret(DINGDING_CONFIG['secret'])

def validate_cookie(bingCK):
    try:
        # 最多尝试3次验证
        max_attempts = VALIDATION_CONFIG['max_attempts']
        failed_attempts = 0
        last_error = None
        
        for attempt in range(max_attempts):
            printLog('调试', f'开始第{attempt + 1}次Cookie验证...')
            balance = getBalance(bingCK)
            printLog('调试-积分响应', str(balance.get('points', '')) if balance else 'None')
            
            if balance and balance.get('points'):
                printLog('验证成功', f'第{attempt + 1}次验证通过')
                return True, '验证通过'
            
            # 记录失败信息
            failed_attempts += 1
            if attempt < max_attempts - 1:  # 如果不是最后一次尝试
                wait_time = random.uniform(VALIDATION_CONFIG['retry_wait_min'], VALIDATION_CONFIG['retry_wait_max'])
                printLog('等待', f'验证失败，{wait_time:.1f}秒后进行第{attempt + 2}次验证')
                time.sleep(wait_time)
            else:
                last_error = '无法获取积分信息'
        
        # 如果三次都失败，发送钉钉通知
        if failed_attempts == max_attempts:
            # 构建通知消息
            user_info = []
            if balance and balance.get('name'):
                user_info.append(f"用户: {balance.get('name')}")
            if balance and balance.get('email'):
                user_info.append(f"邮箱: {balance.get('email')}")
            
            user_info_str = '\n'.join(user_info) if user_info else "未获取到用户信息"
            notify_message = (
                f"⚠️ Bing Cookie失效通知\n"
                f"{user_info_str}\n"
                f"验证次数: {failed_attempts}次\n"
                f"时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            ding.send_text(text=notify_message)
            return False, last_error
            
    except Exception as e:
        # 发送异常通知
        error_message = (
            f"⚠️ Bing Cookie验证异常\n"
            f"错误信息: {str(e)}\n"
            f"时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        ding.send_text(text=error_message)
        return False, f'验证异常: {str(e)}'

# 风险控制
class RiskControl:
    def __init__(self):
        self.continuous_fails = 0
        self.last_success_time = None
        self.daily_searches = 0
        self.suspicious_ips = set()
        self.search_patterns = []
        self.last_search_time = None
        self.search_intervals = []
        self.user_agents_used = set()
        
    def record_search(self, success, search_word, user_agent):
        current_time = time.time()
        
        # 记录基本信息
        if success:
            self.continuous_fails = 0
            self.last_success_time = current_time
            self.daily_searches += 1
        else:
            self.continuous_fails += 1
            
        # 记录搜索模式
        self.search_patterns.append(search_word)
        if len(self.search_patterns) > 10:
            self.search_patterns.pop(0)
            
        # 记录时间间隔
        if self.last_search_time:
            interval = current_time - self.last_search_time
            self.search_intervals.append(interval)
            if len(self.search_intervals) > 10:
                self.search_intervals.pop(0)
        self.last_search_time = current_time
        
        # 记录User-Agent使用情况
        self.user_agents_used.add(user_agent)
            
    def should_stop(self):
        # 基本检查
        if self.continuous_fails >= 5:
            return True, '连续失败次数过多'
            
        if self.daily_searches > 150:
            return True, '达到每日搜索上限'
            
        if self.last_success_time and time.time() - self.last_success_time > 3600:
            return True, '操作超时'
            
        # 检查搜索模式 - 放宽限制
        if len(self.search_patterns) >= 10 and len(set(self.search_patterns[-10:])) < 3:  # 最近10次搜索中完全相同
            return True, '搜索模式异常'
            
        # 检查时间间隔
        if len(self.search_intervals) >= 5:
            avg_interval = sum(self.search_intervals[-5:]) / 5
            if avg_interval < 1:  # 平均间隔小于1秒
                return True, '搜索频率过高'
                
        return False, ''
        
    def reset(self):
        """重置风险控制状态"""
        self.__init__()

# 全局风险控制器
risk_controller = RiskControl()

# 将 calculate_search_times 函数移到文件前面
def calculate_search_times(progress, max_count, current_hour, device_type):
    """计算本次是否需要执行搜索"""
    remaining = max_count - progress
    if remaining <= 0:
        printLog('任务完成', f'✨ {device_type}搜索任务已达到上限({progress}/{max_count})')
        return 0
        
    # 工作时间设置
    working_start = SEARCH_CONFIG['working_hours']['start']  # 使用配置的开始时间
    working_end = SEARCH_CONFIG['working_hours']['end']     # 使用配置的结束时间
    total_hours = working_end - working_start  # 总工作小时数
    
    # 检查是否在工作时间内
    if current_hour < working_start or current_hour > working_end:
        printLog('休息时间', f'🌅 模拟真实用户作息规律: 当前{current_hour}点处于休息时段')
        printLog('智能提示', f'├── 程序将在{working_start}点后开始执行搜索任务')
        printLog('作息建议', f'└── 程序将在{working_start}点后开始活跃的行为特征')
        return 0
    
    # 计算剩余时间
    remaining_hours = working_end - current_hour
    
    # 紧急模式：如果剩余时间不足且还有任务未完成
    is_emergency = remaining_hours <= 2 and remaining > 0
    
    if is_emergency:
        printLog('紧急模式', f'⚠️ 距离{working_end}点仅剩{remaining_hours}小时，进入紧急模式')
        # 在紧急模式下，每次执行更多搜索
        return min(remaining, 5)  # 每次最多执行5次搜索
        
    # 计算当前小时应该完成的理论搜索次数
    current_execution_hour = current_hour - working_start
    if total_hours == 0:
        printLog('配置错误', f'工作时间配置错误: start={working_start}, end={working_end}')
        return 0
    expected_progress = (max_count * current_execution_hour) // total_hours
    
    printLog('进度分析', f'📊 {device_type}搜索进度分析:')
    printLog('时间进度', f'├── 当前时段: {current_hour}:00 (第{current_execution_hour}个工作小时)')
    printLog('任务情况', f'├── 总任务量: {max_count}次搜索/{total_hours}小时 = {max_count/total_hours:.1f}次/小时')
    printLog('理论进度', f'├── 理论进度: {max_count}次 × {current_execution_hour}/{total_hours} = {expected_progress}次')
    printLog('实际进度', f'└── 实际进度: {progress}次 ({(progress/max_count*100):.1f}%完成度)')
    
    # 如果实际进度已经超过理论进度，但剩余时间不足，仍然执行
    if progress >= expected_progress and remaining_hours <= 4:
        printLog('进度调整', f'⚠️ 剩余时间不足，继续执行搜索')
        return min(remaining, 3)  # 每次最多执行3次搜索
    
    # 如果进度落后，计算本次应该执行的次数
    progress_gap = expected_progress - progress
    
    # PC端每次最多4次，移动端最多3次
    max_per_hour = SEARCH_CONFIG['pc_max_per_hour'] if device_type == 'PC' else SEARCH_CONFIG['mobile_max_per_hour']
    times = min(max_per_hour, progress_gap)
    
    printLog('执行计划', f'📈 {device_type}搜索计划:')
    printLog('进度差距', f'├── 当前落后: {progress_gap}次')
    printLog('执行限制', f'├── 单次上限: {max_per_hour}次')
    printLog('本次计划', f'└── 本次执行: {times}次搜索')
    
    return times

def wait_until_afternoon():
    """等待到下午1点"""
    current_time = datetime.datetime.now()
    target_time = current_time.replace(hour=13, minute=0, second=0, microsecond=0)
    
    # 如果当前时间已经过了今天的13点,就不需要等待
    if current_time.hour >= 13:
        return
        
    # 计算需要等待的时间
    wait_seconds = (target_time - current_time).total_seconds()
    if wait_seconds > 0:
        printLog('等待', f'等待到下午1点执行奖励任务,剩余{wait_seconds/3600:.1f}小时')
        time.sleep(wait_seconds)

def startMain(bingCK):
    try:
        # 先验证Cookie有效性
        is_valid, msg = validate_cookie(bingCK)
        if not is_valid:
            printLog('错误', f'Cookie验证失败: {msg}')
            return
            
        # 获取当前时间
        current_time = datetime.datetime.now()
        current_hour = current_time.hour
        
        # 先执行搜索任务
        execute_search_tasks(bingCK)
        
        # 在指定时段执行奖励任务
        if current_hour in REWARD_CONFIG['execution_times']:
            printLog('执行计划', f'当前处于{current_hour}点时段,开始执行奖励任务')
            process_rewards(bingCK)
        else:
            reward_times_str = '、'.join([f'{t}点' for t in REWARD_CONFIG['execution_times']])
            printLog('等待', f'当前时间{current_hour}点,不在指定时段({reward_times_str}),跳过奖励任务')
            
    except Exception as e:
        printLog('错误', f'执行出错: {str(e)}')

def execute_search_tasks(bingCK):
    """执行搜索任务的代码"""
    try:
        # 获取初始积分和进度信息
        result = getBalance(bingCK)
        if not result or not result.get('points'):
            printLog('错误', 'Cookie无效或已过期')
            validate_cookie(bingCK)
            return
            
        # 获取搜索进度
        progress_info = getBalance(bingCK)
        if not progress_info:
            printLog('错误', '无法获取搜索进度')
            return
        
        pc_progress = int(progress_info.get('pc_progress', 0))
        pc_max = int(progress_info.get('pc_max', 90))
        mobile_progress = int(progress_info.get('mobile_progress', 0))
        mobile_max = int(progress_info.get('mobile_max', 60))
        
        # 检查并保存未完成任务
        if pc_progress < pc_max or mobile_progress < mobile_max:
            check_and_save_remaining_tasks(pc_progress, pc_max, mobile_progress, mobile_max)
        
        # 获取当前小时
        current_hour = datetime.datetime.now().hour
        
        # 计算PC端和移动端应执行的次数
        pc_times = calculate_search_times(pc_progress, pc_max, current_hour, 'PC')
        mobile_times = calculate_search_times(mobile_progress, mobile_max, current_hour, '移动端')
        
        # 如果都完成了或当前时段不执行就直接返回
        if pc_times == 0 and mobile_times == 0:
            printLog('等待', '当前时段无需执行搜索')
            return
            
        # 执行PC搜索
        if pc_times > 0:
            printLog('执行计划', f'开始执行PC搜索 {pc_times} 次')
            for i in range(pc_times):
                search_word = get_random_search()
                printLog('搜索进度', f'PC搜索 {i+1}/{pc_times}')
                result = bing_rewards(search_word, bingCK, isPc=True)
                if result == '跳过':
                    break
                # 根据剩余时间调整搜索间隔
                remaining_hours = 24 - current_hour
                if remaining_hours <= 2:  # 紧急模式
                    sleep_time = random.uniform(3, 6)  # 缩短等待时间
                else:
                    sleep_time = random.uniform(5, 12)
                printLog('等待', f'下次搜索等待{sleep_time:.1f}秒')
                time.sleep(sleep_time)
                    
        # 如果需要执行移动端搜索，添加一个较长的间隔（15-30秒）
        if pc_times > 0 and mobile_times > 0:
            remaining_hours = 24 - current_hour
            if remaining_hours <= 2:  # 紧急模式
                sleep_time = random.uniform(8, 15)  # 缩短等待时间
            else:
                sleep_time = random.uniform(15, 30)
            printLog('等待', f'切换到移动端搜索，等待{sleep_time:.1f}秒')
            time.sleep(sleep_time)
            
        # 执行移动端搜索
        if mobile_times > 0:
            printLog('执行计划', f'开始执行移动端搜索 {mobile_times} 次')
            for i in range(mobile_times):
                search_word = get_random_search()
                printLog('搜索进度', f'移动端搜索 {i+1}/{mobile_times}')
                result = bing_rewards(search_word, bingCK, isPc=False)
                if result == '跳过':
                    break
                
                # 根据剩余时间调整搜索间隔
                remaining_hours = 24 - current_hour
                if remaining_hours <= 2:  # 紧急模式
                    sleep_time = random.uniform(4, 8)  # 缩短等待时间
                else:
                    sleep_time = random.uniform(8, 15)
                printLog('等待', f'移动端下次搜索等待{sleep_time:.1f}秒')
                time.sleep(sleep_time)
                
                # 每3次搜索后增加一个较长的休息时间
                if (i + 1) % 3 == 0 and i < mobile_times - 1:
                    if remaining_hours <= 2:  # 紧急模式
                        rest_time = random.uniform(10, 15)  # 缩短休息时间
                    else:
                        rest_time = random.uniform(20, 30)
                    printLog('休息', f'移动端搜索休息{rest_time:.1f}秒')
                    time.sleep(rest_time)
            
    except Exception as e:
        import traceback
        printLog('错误', f'搜索任务执行出错: {str(e)}')
        printLog('调试', f'错误详情: {traceback.format_exc()}')

# 工具函数
def calculate_points_gained(old_points, new_points):
    """计算积分增长"""
    try:
        return int(new_points) - int(old_points)
    except:
        return 0

def safe_str(value):
    """安全字符串转换"""
    return str(value) if value is not None else ''
            
#输出日志   
def printLog(title, msg):
    """美化日志输出"""
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 定义不同类型日志的图标和格式
    icons = {
        '执行模式': '🔄',
        '调试': '🔍',
        '账号信息': '👤',
        '当前积分': '💎',
        '当前用户组': '⭐',
        'PC搜索进度': '🖥️',
        '移动搜索进度': '📱',
        '进度分析': '📊',
        '时间进度': '⏱️',
        '任务情况': '📋',
        '理论进度': '📈',
        '实际进度': '📉',
        '休息时间': '⏰',
        '智能提示': '💡',
        '作息建议': '🌟',
        '进度超前': '🚀',
        '执行计划': '📋',
        '进度差距': '⚖️',
        '执行限制': '🔒',
        '本次计划': '✨',
        '任务完成': '🎉',
        '错误': '❌',
        '异常': '⚠️',
        '重试': '🔄',
        '等待': '⏳'
    }
    
    # 获取对应的图标,默认使用 📌
    icon = icons.get(title, '📌')
    
    if COLORED_OUTPUT:
        if '错误' in title or '异常' in title:
            print(f"{Fore.RED}{now} [{icon} {title}]: {safe_str(msg)}{Style.RESET_ALL}")
        elif '完成' in title or '超前' in title:
            print(f"{Fore.GREEN}{now} [{icon} {title}]: {safe_str(msg)}{Style.RESET_ALL}")
        elif '进度' in title or '分析' in title:
            print(f"{Fore.CYAN}{now} [{icon} {title}]: {safe_str(msg)}{Style.RESET_ALL}")
        elif '等待' in title or '休息' in title:
            print(f"{Fore.YELLOW}{now} [{icon} {title}]: {safe_str(msg)}{Style.RESET_ALL}")
        elif '智能' in title or '提示' in title:
            print(f"{Fore.MAGENTA}{now} [{icon} {title}]: {safe_str(msg)}{Style.RESET_ALL}")
        else:
            print(f"{Fore.WHITE}{now} [{icon} {title}]: {safe_str(msg)}{Style.RESET_ALL}")
    else:
        # 使用 ANSI 转义序列作为后备
        if '错误' in title or '异常' in title:
            print(f"\033[91m{now} [{icon} {title}]: {safe_str(msg)}\033[0m")
        elif '完成' in title or '超前' in title:
            print(f"\033[92m{now} [{icon} {title}]: {safe_str(msg)}\033[0m")
        elif '进度' in title or '分析' in title:
            print(f"\033[96m{now} [{icon} {title}]: {safe_str(msg)}\033[0m")
        elif '等待' in title or '休息' in title:
            print(f"\033[93m{now} [{icon} {title}]: {safe_str(msg)}\033[0m")
        elif '智能' in title or '提示' in title:
            print(f"\033[95m{now} [{icon} {title}]: {safe_str(msg)}\033[0m")
        else:
            print(f"{now} [{icon} {title}]: {safe_str(msg)}")

# Cookie配置
COOKIE_CONFIG = {
    'pc_whitelist': [
        'MUID', 'ANON', '_U', '_SS', '_EDGE_S', '_EDGE_V', 'MUIDB', 'SRCHD', 'SRCHUID',
        'SRCHUSR', 'SRCHHPGUSR', 'tifacfaatcs', '.AspNetCore.Antiforgery.icPscOZlg04'
    ],
    'mobile_whitelist': [
        'MUID', 'ANON', '_U', '_SS', '_EDGE_S', '_EDGE_V', 'MUIDB', 'SRCHD', 'SRCHUID',
        'SRCHUSR', 'SRCHHPGUSR', 'tifacfaatcs', '.AspNetCore.Antiforgery.icPscOZlg04'
    ],
    'reward_whitelist': [
        'MUID', 'ANON', '_U', '_SS', '_EDGE_S', '_EDGE_V', 'MUIDB', 'SRCHD', 'SRCHUID',
        'SRCHUSR', 'SRCHHPGUSR', 'tifacfaatcs', '.AspNetCore.Antiforgery.icPscOZlg04',
        'webisession', 'GC', '_RwBf', '_Rwho'
    ]
}

def filter_cookies(cookie_string, filter_type='pc'):
    """根据类型过滤Cookie"""
    # 确保输入是字符串
    if isinstance(cookie_string, list):
        cookie_string = cookie_string[0] if cookie_string else ""

    if not isinstance(cookie_string, str):
        return ""

    whitelist = COOKIE_CONFIG.get(f'{filter_type}_whitelist', COOKIE_CONFIG['pc_whitelist'])

    cookie_dict = {}
    for cookie in cookie_string.split(';'):
        cookie = cookie.strip()
        if '=' in cookie:
            name, value = cookie.split('=', 1)
            name = name.strip()
            if name in whitelist:
                cookie_dict[name] = value

    return '; '.join([f"{name}={value}" for name, value in cookie_dict.items()])

def read_cookies_from_file(file_path='cookie.txt'):
    """读取Cookie文件并返回原始Cookie字符串"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                printLog('错误', 'Cookie文件为空')
                return []

            cookies = []
            for line in content.split('\n'):
                line = line.strip()
                if line:
                    cookies.append(line)

            return cookies
    except FileNotFoundError:
        return []
    except Exception as e:
        printLog('错误', f'读取Cookie文件失败: {str(e)}')
        return []

# 删除复杂的参数解析，使用简单配置

def get_request_verification_token(response_text):
    """从响应中提取 __RequestVerificationToken"""
    try:
        pattern = r'<input name="__RequestVerificationToken" type="hidden" value="([^"]+)"'
        match = re.search(pattern, response_text)
        if match:
            return match.group(1)
        return None
    except Exception as e:
        printLog('错误', f'获取 RequestVerificationToken 出错: {e}')
        return None

def visit_activity_url(url, cookies):
    """访问活动URL"""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Referer": "https://rewards.bing.com/",
        "Cookie": cookies
    }

    try:
        # 第一次访问
        response = requests.get(url, headers=headers)
        printLog('签到认证', f'第一次认证状态: {response.status_code}')

        time.sleep(random.uniform(2, 4))

        # 第二次访问确认
        response = requests.get(url, headers=headers)
        printLog('签到认证', f'确认认证状态: {response.status_code}')
        
        if response.status_code == 200:
            printLog('签到认证', '✅ 签到认证完成')
            return True
        else:
            printLog('签到认证', '❌ 签到认证失败')
        return False
        
    except Exception as e:
        printLog('错误', f'签到认证出错: {e}')
        return False

def complete_activity(offer_id, hash_value, url, cookies, token, headers):
    """完成活动"""
    printLog('活动', f'开始处理活动: {offer_id}')

    # 为奖励任务使用专门的Cookie过滤
    filtered_cookies = filter_cookies(cookies, 'reward')

    if not visit_activity_url(url, filtered_cookies):
        printLog('错误', '签到认证失败')
        return False

    time.sleep(random.uniform(2, 4))

    # 构建请求数据
    data = {
        "id": offer_id,
        "hash": hash_value,
        "timeZone": "480",
        "activityAmount": "1",
        "dbs": "0",
        "form": "",
        "type": "",
        "__RequestVerificationToken": token
    }

    try:
        api_url = "https://rewards.bing.com/api/reportactivity?X-Requested-With=XMLHttpRequest"

        # 简化的请求头
        api_headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Origin": "https://rewards.bing.com",
            "Referer": "https://rewards.bing.com/",
            "X-Requested-With": "XMLHttpRequest",
            "Cookie": filtered_cookies
        }

        response = requests.post(api_url, headers=api_headers, data=data)

        if response.status_code == 200:
            try:
                result = response.json()
                printLog('完成状态', '成功')
                printLog('积分', f'当前积分: {result.get("balance", "未知")}')
                return result
            except json.JSONDecodeError as e:
                printLog('错误', f'JSON解析错误: {e}')
                printLog('响应', f'响应内容: {response.text[:200]}')
                return None
        else:
            printLog('错误', f'请求失败，状态码: {response.status_code}')
            printLog('响应内容', response.text[:200])
            return None

    except Exception as e:
        printLog('错误', f'完成活动出错: {e}')
        return None

def process_rewards(bingCK):
    """处理奖励活动"""
    try:
        # 为奖励任务过滤Cookie
        filtered_cookies = filter_cookies(bingCK, 'reward')

        # 获取当前时间用于日志
        current_hour = datetime.datetime.now().hour
        period = "下午" if current_hour == 13 else "晚上"
        printLog('时段信息', f'正在执行{period}时段的奖励任务')

        # 获取 token
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Referer": "https://rewards.bing.com/",
            "Cookie": filtered_cookies
        }

        response = requests.get("https://rewards.bing.com", headers=headers)
        token = get_request_verification_token(response.text)
        
        if not token:
            printLog('错误', '获取token失败')
            return
            
        # 获取活动数据
        url = "https://rewards.bing.com/?ref=rewardspanel"
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            pattern = r'var\s+dashboard\s*=\s*({.*?});'
            match = re.search(pattern, response.text, re.DOTALL)
            
            if match:
                dashboard_data = json.loads(match.group(1))
                
                # 处理所有活动
                promotions = []
                if 'promotions' in dashboard_data:
                    promotions.extend(dashboard_data['promotions'])
                if 'morePromotions' in dashboard_data:
                    promotions.extend(dashboard_data['morePromotions'])
                if 'dailySetPromotions' in dashboard_data:
                    for date, promos in dashboard_data['dailySetPromotions'].items():
                        promotions.extend(promos)
                
                # 完成活动
                for promo in promotions:
                    if 'offerId' not in promo or promo.get('complete', False):
                        continue
                        
                    activity_info = {
                        '标题': promo.get('title', '').strip(),  # 添加 strip() 去除可能的空白字符
                        'URL': promo.get('destinationUrl', ''),
                        '积分': promo.get('pointProgressMax', 0),
                        'HASH': promo.get('hash', ''),
                        'ID': promo.get('offerId', '')
                    }
                    
                    # 修改输出格式,移除换行符
                    printLog('活动信息', activity_info['标题'])
                    
                    if activity_info['HASH']:
                        complete_activity(
                            activity_info['ID'],
                            activity_info['HASH'],
                            activity_info['URL'],
                            filtered_cookies,
                            token,
                            headers
                        )
                        time.sleep(random.uniform(3, 6))
            else:
                printLog('错误', '未找到活动数据')
        else:
            printLog('错误', f'获取活动数据失败,状态码: {response.status_code}')
            
    except Exception as e:
        printLog('错误', f'处理奖励出错: {e}')

# 添加到文件开头的全局变量部分
class TaskStatus:
    def __init__(self):
        self.pc_remaining = 0
        self.mobile_remaining = 0
        self.last_check_time = None

# 创建任务状态实例
task_status = TaskStatus()

def check_and_save_remaining_tasks(pc_progress, pc_max, mobile_progress, mobile_max):
    """检查并保存未完成的任务"""
    global task_status
    task_status.pc_remaining = pc_max - pc_progress
    task_status.mobile_remaining = mobile_max - mobile_progress
    task_status.last_check_time = datetime.datetime.now()
    printLog('任务状态', f'保存未完成任务: PC剩余{task_status.pc_remaining}次, 移动端剩余{task_status.mobile_remaining}次')

def execute_remaining_tasks(bingCK):
    """执行剩余的任务"""
    if task_status.pc_remaining > 0 or task_status.mobile_remaining > 0:
        printLog('任务恢复', f'开始执行剩余任务: PC剩余{task_status.pc_remaining}次, 移动端剩余{task_status.mobile_remaining}次')
        execute_search_tasks(bingCK)
        task_status.pc_remaining = 0
        task_status.mobile_remaining = 0

class RequestHeaderGenerator:
    def __init__(self):
        self.last_rotation = time.time()
        self.rotation_interval = random.randint(300, 600)  # 5-10分钟轮换一次
        self.current_headers = None
        self.browser_profiles = self._load_browser_profiles()
        self.connection_types = ['wifi', '5g', '4g', 'ethernet']
        self.screen_resolutions = ['1920x1080', '2560x1440', '1366x768', '1440x900']
        
    def _load_browser_profiles(self):
        return {
            'edge': {
                'versions': ['108.0.1462.54', '109.0.1518.78', '110.0.1587.41', 
                           '111.0.1661.44', '112.0.1722.39', '113.0.1774.35'],
                'builds': ['24.1', '24.2', '24.3', '24.4', '24.5'],
                'channels': ['stable', 'beta', 'dev']
            },
            'chrome': {
                'versions': ['*********', '*********', '110.0.0.0', 
                           '*********', '*********', '*********'],
                'builds': ['5304.47', '5304.85', '5304.110']
            }
        }

    def _generate_device_info(self, is_pc=True):
        if is_pc:
            return {
                'platform': 'Windows',
                'version': random.choice(['10.0', '11.0']),
                'architecture': random.choice(['x86_64', 'arm64']),
                'model': '',
                'bitness': '64',
                'screen': random.choice(self.screen_resolutions)
            }
        else:
            devices = [
                {'platform': 'Android', 'version': '13', 'model': 'SM-S908B'},
                {'platform': 'Android', 'version': '12', 'model': 'Mi 10 Pro'},
                {'platform': 'iOS', 'version': '16.4.1', 'model': 'iPhone'},
                {'platform': 'Android', 'version': '13', 'model': 'Pixel 7'},
                {'platform': 'Android', 'version': '13', 'model': 'IN2023'}
            ]
            device = random.choice(devices)
            device['screen'] = random.choice(['412x915', '390x844', '360x800'])
            return device

    def _generate_browser_info(self, is_pc=True):
        profile = self.browser_profiles
        edge_ver = random.choice(profile['edge']['versions'])
        chrome_ver = random.choice(profile['chrome']['versions'])
        edge_build = random.choice(profile['edge']['builds'])
        edge_channel = random.choice(profile['edge']['channels'])
        
        return {
            'edge_version': edge_ver,
            'chrome_version': chrome_ver,
            'edge_build': edge_build,
            'edge_channel': edge_channel,
            'sec_ch_ua': f'"Not A(Brand";v="99", "Microsoft Edge";v="{edge_ver.split(".")[0]}", "Chromium";v="{chrome_ver.split(".")[0]}"',
            'sec_ch_ua_full_version': edge_ver,
            'sec_ch_ua_full_version_list': f'"Not A(Brand";v="99.0.0.0", "Microsoft Edge";v="{edge_ver}", "Chromium";v="{chrome_ver}"'
        }

    def _generate_connection_info(self):
        connection_type = random.choice(self.connection_types)
        downlink = random.uniform(1.5, 15.0) if connection_type in ['wifi', 'ethernet'] else random.uniform(0.5, 5.0)
        rtt = random.randint(50, 200) if connection_type in ['wifi', 'ethernet'] else random.randint(100, 500)
        
        return {
            'connection_type': connection_type,
            'downlink': round(downlink, 2),
            'rtt': rtt,
            'ect': '4g' if connection_type == '4g' else 'unknown'
        }

    def _generate_fingerprint(self, is_pc=True):
        device = self._generate_device_info(is_pc)
        browser = self._generate_browser_info(is_pc)
        connection = self._generate_connection_info()
        
        canvas_noise = ''.join(random.choices('0123456789abcdef', k=32))
        audio_noise = ''.join(random.choices('0123456789abcdef', k=32))
        webgl_noise = ''.join(random.choices('0123456789abcdef', k=32))
        
        return {
            'device': device,
            'browser': browser,
            'connection': connection,
            'canvas_fp': canvas_noise,
            'audio_fp': audio_noise,
            'webgl_fp': webgl_noise,
            'timezone': 'Asia/Shanghai',
            'language': 'zh-CN',
            'platform': device['platform']
        }

    def get_headers(self, is_pc=True, force_rotate=False):
        current_time = time.time()
        should_rotate = force_rotate or (current_time - self.last_rotation > self.rotation_interval)
        
        if should_rotate or not self.current_headers:
            fingerprint = self._generate_fingerprint(is_pc)
            browser = fingerprint['browser']
            device = fingerprint['device']
            connection = fingerprint['connection']
            
            if is_pc:
                user_agent = f"Mozilla/5.0 (Windows NT {device['version']}; {device['architecture']}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser['chrome_version']} Safari/537.36 Edg/{browser['edge_version']}"
            else:
                if device['platform'] == 'iOS':
                    user_agent = f"Mozilla/5.0 (iPhone; CPU iPhone OS {device['version'].replace('.', '_')} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Mobile/15E148 Safari/604.1 EdgiOS/{browser['edge_version']}"
                else:
                    user_agent = f"Mozilla/5.0 (Linux; Android {device['version']}; {device['model']}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{browser['chrome_version']} Mobile Safari/537.36 EdgA/{browser['edge_version']}"

            headers = {
                "User-Agent": user_agent,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "DNT": "1",
                "Upgrade-Insecure-Requests": "1",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-User": "?1",
                "Sec-Fetch-Dest": "document",
                "Sec-Ch-Ua": browser['sec_ch_ua'],
                "Sec-Ch-Ua-Mobile": "?1" if not is_pc else "?0",
                "Sec-Ch-Ua-Full-Version": browser['sec_ch_ua_full_version'],
                "Sec-Ch-Ua-Arch": device['architecture'] if is_pc else "",
                "Sec-Ch-Ua-Platform": device['platform'],
                "Sec-Ch-Ua-Platform-Version": device['version'],
                "Sec-Ch-Ua-Model": device['model'],
                "Sec-Ch-Ua-Bitness": device['bitness'] if is_pc else "",
                "Sec-Ch-Ua-Full-Version-List": browser['sec_ch_ua_full_version_list'],
                "X-Edge-Shopping-Flag": "1",
                "X-Edge-UA": device['platform'],
                "X-MSEdge-ExternalExpType": "JointCoord",
                "X-MSEdge-ExternalExp": "001,h2c",
                "X-MSEdge-DeviceFamily": device['platform'],
                "Priority": "u=0, i"
            }

            # 添加连接信息
            if connection['connection_type'] != 'unknown':
                headers.update({
                    "Downlink": str(connection['downlink']),
                    "RTT": str(connection['rtt']),
                    "ECT": connection['ect'],
                    "Save-Data": "on" if random.random() < 0.2 else "off"
                })

            # 移动端特有头部
            if not is_pc:
                headers.update({
                    "X-Requested-With": "com.microsoft.emmx",
                    "X-MSEdge-Market": "ZH-CN",
                    "X-MSEdge-ClientID": ''.join(random.choices('0123456789ABCDEF', k=32)),
                    "X-MSEdge-UserState": "0,{},264".format(int(time.time()))
                })

            self.current_headers = headers
            self.last_rotation = current_time
            self.rotation_interval = random.randint(300, 600)  # 重置轮换间隔

        return self.current_headers.copy()  # 返回headers的副本以防止修改

class RequestParameterManager:
    def __init__(self):
        self.last_params = None
        self.last_update = time.time()
        self.update_interval = random.randint(180, 300)  # 3-5分钟更新一次
        self.search_patterns = self._init_search_patterns()
        self.interaction_history = []
        
    def _init_search_patterns(self):
        return {
            'normal': {
            'form': 'QBLH',
            'sp': '-1',
                'ghc': '1',
                'lq': '0',
                'pq': '',
                'sc': '8-1',
                'qs': 'n',
            'sk': '',
                'cvid': '',
                'mkt': 'zh-CN'
            },
            'suggestion': {
                'form': 'AS',
                'qsc': '8',
                'sp': '-1',
                'pq': '',
                'sc': '8-1',
            'qs': 'n',
                'cvid': ''
            },
            'related': {
                'form': 'QBRE',
                'sp': '-1',
                'pq': '',
                'sc': '8-1',
                'qs': 'n',
                'sk': '',
                'cvid': '',
                'ghc': '1'
            }
        }

    def _generate_cvid(self):
        """生成随机的客户端标识"""
        timestamp = hex(int(time.time()))[2:]
        random_hex = ''.join(random.choices('0123456789ABCDEF', k=16))
        return f"{timestamp}{random_hex}"

    def _generate_session_id(self):
        """生成会话标识"""
        return ''.join(random.choices('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=32))

    def _get_search_context(self, query, prev_query=None):
        """生成搜索上下文"""
        context = {
            'original_query': query,
            'refined_query': query,
            'session_id': self._generate_session_id(),
            'page_number': 1,
            'results_per_page': 10,
            'search_type': 'normal',
            'last_interaction': time.time()
        }
        
        # 如果有前一次查询，添加相关性信息
        if prev_query:
            context['prev_query'] = prev_query
            context['query_sequence'] = len(self.interaction_history) + 1
            
            # 分析查询相关性
            if any(word in query for word in prev_query.split()):
                context['search_type'] = 'related'
                context['relation_type'] = 'refinement'
            elif len(query) < len(prev_query):
                context['search_type'] = 'suggestion'
                context['relation_type'] = 'suggestion'
        
        return context

    def _adjust_parameters(self, params, context):
        """根据上下文调整参数"""
        current_time = time.time()
        
        # 根据搜索类型选择基础参数模板
        base_params = self.search_patterns[context['search_type']].copy()
        
        # 添加查询相关参数
        base_params.update({
            'q': context['refined_query'],
            'pq': context.get('prev_query', ''),
            'cvid': self._generate_cvid(),
            'first': str((context['page_number'] - 1) * context['results_per_page'] + 1),
            'count': str(context['results_per_page'])
        })
        
        # 添加时间戳和随机性
        if random.random() < 0.3:  # 30%概率添加时间过滤
            time_ranges = ['day', 'week', 'month']
            base_params['qft'] = f"+filterui:age-{random.choice(time_ranges)}"
        
        # 添加地区和语言偏好
        if random.random() < 0.4:  # 40%概率添加地区限制
            base_params['cc'] = 'CN'
            base_params['setlang'] = 'zh-CN'
        
        # 添加搜索建议相关参数
        if context['search_type'] == 'suggestion':
            base_params.update({
                'ss': '1',
                'nclid': self._generate_session_id()[:16]
            })
        
        # 添加相关搜索参数
        if context['search_type'] == 'related':
            base_params.update({
                'rdr': '1',
                'rdrig': self._generate_session_id()[:8]
            })
        
        return base_params

    def _record_interaction(self, context, params):
        """记录搜索交互历史"""
        interaction = {
            'timestamp': time.time(),
            'query': context['original_query'],
            'search_type': context['search_type'],
            'session_id': context['session_id'],
            'parameters': params.copy()
        }
        self.interaction_history.append(interaction)
        
        # 只保留最近50次交互记录
        if len(self.interaction_history) > 50:
            self.interaction_history = self.interaction_history[-50:]

    def get_search_parameters(self, query, prev_query=None, force_update=False):
        """获取搜索参数"""
        current_time = time.time()
        should_update = force_update or (current_time - self.last_update > self.update_interval)
        
        # 生成搜索上下文
        context = self._get_search_context(query, prev_query)
        
        if should_update or not self.last_params:
            # 生成新的参数集
            params = self._adjust_parameters({}, context)
            self.last_params = params
            self.last_update = current_time
            self.update_interval = random.randint(180, 300)
        else:
            # 复用并微调上次的参数
            params = self.last_params.copy()
            params.update({
                'q': context['refined_query'],
                'cvid': self._generate_cvid(),
                'first': str((context['page_number'] - 1) * context['results_per_page'] + 1)
            })
        
        # 记录交互
        self._record_interaction(context, params)
        
        return params

    def get_request_config(self, query, is_pc=True, prev_query=None):
        """获取完整的请求配置"""
        headers = RequestHeaderGenerator().get_headers(is_pc=is_pc)
        params = self.get_search_parameters(query, prev_query)
        
        return {
            'headers': headers,
            'params': params,
            'timeout': random.uniform(3, 8),
            'allow_redirects': True
        }

# 创建请求管理器实例
request_manager = RequestHeaderGenerator()
param_manager = RequestParameterManager()

def get_request_config(query, is_pc=True, force_rotate=False):
    """获取完整的请求配置"""
    headers = request_manager.get_headers(is_pc, force_rotate)
    params = param_manager.get_search_parameters(query)  # 修正方法名
    return headers, params

class RequestDelayManager:
    def __init__(self):
        self.last_request_time = None
        self.request_count = 0
        self.delay_pattern = 'normal'  # normal, cautious, conservative
        
    def get_delay(self, risk_level='low'):
        """获取下一次请求的延迟时间"""
        current_time = time.time()
        
        # 根据风险级别调整延迟模式
        if risk_level == 'high':
            self.delay_pattern = 'conservative'
        elif risk_level == 'medium':
            self.delay_pattern = 'cautious'
        
        # 基础延迟时间
        if self.delay_pattern == 'conservative':
            base_delay = random.uniform(15, 25)
        elif self.delay_pattern == 'cautious':
            base_delay = random.uniform(10, 18)
        else:
            base_delay = random.uniform(5, 12)
            
        # 添加请求计数器的影响
        if self.request_count > 10:
            base_delay *= 1.5
            
        # 如果是第一次请求
        if self.last_request_time is None:
            self.last_request_time = current_time
            return base_delay
            
        # 计算实际延迟
        time_since_last = current_time - self.last_request_time
        if time_since_last < base_delay:
            actual_delay = base_delay - time_since_last
        else:
            actual_delay = random.uniform(1, 3)  # 最小延迟
            
        self.last_request_time = current_time
        self.request_count += 1
        
        # 每10次请求后重置计数
        if self.request_count >= 10:
            self.request_count = 0
            actual_delay += random.uniform(10, 20)  # 添加额外休息时间
            
        return actual_delay
        
    def reset(self):
        """重置延迟管理器"""
        self.last_request_time = None
        self.request_count = 0
        self.delay_pattern = 'normal'

# 创建管理器实例
delay_manager = RequestDelayManager()

def make_request(url, method='GET', headers=None, params=None, data=None, timeout=10):
    """统一的请求处理函数"""
    try:
        # 获取延迟时间
        delay = delay_manager.get_delay()
        printLog('请求延迟', f'等待 {delay:.1f} 秒后发送请求')
        time.sleep(delay)
        
        # 发送请求
        response = requests.request(
            method=method,
            url=url,
            headers=headers,
            params=params,
            data=data,
            timeout=timeout
        )
        
        # 检查响应
        if response.status_code == 200:
            return response
        else:
            printLog('请求异常', f'状态码: {response.status_code}')
            delay_manager.delay_pattern = 'cautious'  # 切换到谨慎模式
            return None
            
    except requests.RequestException as e:
        printLog('网络异常', str(e))
        delay_manager.delay_pattern = 'conservative'  # 切换到保守模式
        return None
    except Exception as e:
        printLog('请求错误', str(e))
        return None

def get_cookie_from_url():
    """从指定的URL获取Cookie"""
    url = "https://bing-cookie-sync-worker.5s6.com/?token=a778899a"
    try:
        printLog('Cookie来源', '1. 正在尝试从 URL 获取...')
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        cookie = response.text.strip()
        if cookie:
            printLog('信息', '成功从URL获取Cookie')
            return cookie
        else:
            printLog('警告', '从URL获取的Cookie为空')
            return None
    except requests.RequestException as e:
        printLog('错误', f'从URL获取Cookie失败: {e}')
        return None

def read_cookies_from_file(file_path='cookie.txt'):
    """读取Cookie文件并返回原始Cookie字符串列表"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                # 文件为空不是错误，只是没有Cookie
                return []

            cookies = [line.strip() for line in content.split('\n') if line.strip()]
            if cookies:
                printLog('信息', f'成功从文件获取 {len(cookies)} 个Cookie')
            return cookies
    except FileNotFoundError:
        # 文件不存在不是错误
        return []
    except Exception as e:
        printLog('错误', f'读取Cookie文件失败: {str(e)}')
        return []

def get_prioritized_cookies():
    """按照优先级获取所有可用Cookie：URL > 环境变量 > 文件"""
    cookies = []

    # 1. 从URL获取
    url_cookie = get_cookie_from_url()
    if url_cookie:
        cookies.append(url_cookie)

    # 2. 从环境变量获取
    printLog('Cookie来源', '2. 正在尝试从环境变量 BING_COOKIE 获取...')
    env_cookie = os.getenv('BING_COOKIE')
    if env_cookie and env_cookie.strip():
        # 避免添加重复
        if env_cookie.strip() not in cookies:
            cookies.append(env_cookie.strip())
            printLog('信息', '成功从环境变量获取Cookie')

    # 3. 从文件获取
    printLog('Cookie来源', '3. 正在尝试从 cookie.txt 文件获取...')
    file_cookies = read_cookies_from_file()
    for fc in file_cookies:
        if fc.strip() and fc.strip() not in cookies:
            cookies.append(fc.strip())
    
    if cookies:
        printLog('Cookie汇总', f'共获取到 {len(cookies)} 个候选Cookie')
    
    return cookies

def main():
    """主程序入口 - 简化版单账号模式"""
    printLog('程序启动', 'Bing Rewards 自动化脚本启动')
    printLog('执行模式', '单账号专注模式')

    try:
        # 按优先级获取所有可用Cookie
        all_cookies = get_prioritized_cookies()

        if not all_cookies:
            printLog('错误', '无可用Cookie。请检查URL、BING_COOKIE环境变量或cookie.txt文件')
            return

        # 遍历所有Cookie，找到一个有效的并执行任务
        for i, bingCK in enumerate(all_cookies):
            printLog('账号验证', f'开始验证第 {i + 1}/{len(all_cookies)} 个Cookie...')
            is_valid, msg = validate_cookie(bingCK)
            if is_valid:
                printLog('验证成功', f'Cookie {i + 1} 验证通过，将使用此Cookie执行任务。')
                startMain(bingCK)
                printLog('程序完成', '当前账号任务已执行完毕。')
                return  # 找到一个有效的就执行完退出
            else:
                printLog('验证失败', f'Cookie {i + 1} 无效: {msg}')
        
        printLog('严重错误', '所有候选Cookie都已失效，程序退出。')

    except KeyboardInterrupt:
        printLog('程序中断', '用户手动停止程序')
    except Exception as e:
        printLog('程序异常', f'运行出错: {str(e)}')

if __name__ == '__main__':
    main()