#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理测试文件脚本
用于删除测试过程中创建的临时文件
"""

import os
import sys

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_fix.py',
        'test_search_example.py',
        'cleanup_test_files.py',
        '__pycache__'
    ]
    
    print("开始清理测试文件...")
    
    for file_path in test_files:
        try:
            if os.path.isfile(file_path):
                os.remove(file_path)
                print(f"✅ 已删除文件: {file_path}")
            elif os.path.isdir(file_path):
                import shutil
                shutil.rmtree(file_path)
                print(f"✅ 已删除目录: {file_path}")
            else:
                print(f"⚠️ 文件不存在: {file_path}")
        except Exception as e:
            print(f"❌ 删除失败 {file_path}: {e}")
    
    print("\n清理完成！")
    print("保留的文件:")
    print("- la_auto_10_dingding..py (主脚本)")
    print("- README.md (使用说明)")
    print("- cookie.txt (如果存在)")

if __name__ == "__main__":
    print("BING积分脚本测试文件清理工具")
    print("=" * 40)
    
    confirm = input("确认要删除测试文件吗？(y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        cleanup_test_files()
    else:
        print("取消清理操作")
