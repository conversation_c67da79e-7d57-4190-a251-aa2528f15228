#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BING积分脚本测试示例
用于测试修复后的搜索功能是否正常工作
"""

import sys
import os
import importlib.util

def load_bing_script():
    """加载BING脚本模块"""
    spec = importlib.util.spec_from_file_location("bing_script", "la_auto_10_dingding..py")
    bing_script = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(bing_script)
    return bing_script

def test_with_cookie(cookie_string):
    """使用提供的Cookie测试搜索功能"""
    try:
        print("=== 加载BING脚本模块 ===")
        bing_script = load_bing_script()
        print("✅ 模块加载成功")
        
        print("\n=== 验证Cookie有效性 ===")
        is_valid, msg = bing_script.validate_cookie(cookie_string)
        if not is_valid:
            print(f"❌ Cookie验证失败: {msg}")
            return False
        print("✅ Cookie验证通过")
        
        print("\n=== 测试搜索请求 ===")
        test_result = bing_script.test_search_request(cookie_string, "天气预报")
        if test_result:
            print("✅ 搜索请求测试成功")
        else:
            print("❌ 搜索请求测试失败")
            return False
        
        print("\n=== 执行单次搜索测试 ===")
        result = bing_script.bing_rewards("测试搜索", cookie_string, isPc=True)
        print(f"搜索结果: {result}")
        
        if result == '成功':
            print("🎉 搜索功能正常！修复生效！")
            return True
        else:
            print("⚠️ 搜索可能存在问题，请检查")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def read_cookie_from_file():
    """从cookie.txt文件读取Cookie"""
    try:
        with open('cookie.txt', 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if content:
                lines = content.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        return line
        return None
    except FileNotFoundError:
        print("❌ 未找到cookie.txt文件")
        return None
    except Exception as e:
        print(f"❌ 读取cookie.txt文件失败: {e}")
        return None

def main():
    """主函数"""
    print("BING积分脚本搜索功能测试")
    print("=" * 50)
    
    # 尝试从文件读取Cookie
    cookie = read_cookie_from_file()
    
    if not cookie:
        print("\n请选择Cookie输入方式:")
        print("1. 手动输入Cookie")
        print("2. 创建cookie.txt文件")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == '1':
            cookie = input("请输入Cookie: ").strip()
        elif choice == '2':
            print("\n请创建cookie.txt文件，并在其中添加有效的Cookie")
            print("Cookie格式示例: MUID=xxx; _U=xxx; _SS=xxx; ...")
            return
        else:
            print("无效选择")
            return
    
    if not cookie:
        print("❌ 未提供有效的Cookie")
        return
    
    print(f"\n使用Cookie: {cookie[:50]}...")
    
    # 执行测试
    success = test_with_cookie(cookie)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试完成！修复生效，可以正常运行脚本了。")
        print("\n下一步:")
        print("1. 运行完整脚本: python la_auto_10_dingding..py")
        print("2. 观察积分增长情况")
        print("3. 检查钉钉通知（如已配置）")
    else:
        print("⚠️ 测试失败，请检查:")
        print("1. Cookie是否有效且完整")
        print("2. 网络连接是否正常")
        print("3. 是否被BING检测限制")

if __name__ == "__main__":
    main()
