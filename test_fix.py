#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BING积分脚本修复验证测试
测试修复后的关键功能是否正常工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入功能"""
    try:
        print("=== 测试模块导入 ===")
        
        # 测试基础导入
        import requests
        import random
        import time
        import json
        print("✅ 基础模块导入成功")
        
        # 测试脚本导入 - 使用importlib处理特殊文件名
        import importlib.util
        spec = importlib.util.spec_from_file_location("bing_script", "la_auto_10_dingding..py")
        bing_script = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(bing_script)

        # 验证关键函数存在
        assert hasattr(bing_script, 'get_request_config')
        assert hasattr(bing_script, 'filter_cookies')
        assert hasattr(bing_script, 'USER_AGENTS')
        assert hasattr(bing_script, 'MOBILE_USER_AGENTS')
        print("✅ 脚本模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_user_agents():
    """测试User-Agent更新"""
    try:
        print("\n=== 测试User-Agent版本 ===")

        # 导入模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("bing_script", "la_auto_10_dingding..py")
        bing_script = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(bing_script)

        USER_AGENTS = bing_script.USER_AGENTS
        MOBILE_USER_AGENTS = bing_script.MOBILE_USER_AGENTS
        
        # 检查PC端User-Agent
        pc_ua = USER_AGENTS[0]
        if "*********" in pc_ua:
            print("✅ PC端User-Agent已更新到最新版本")
        else:
            print(f"⚠️ PC端User-Agent版本可能过旧: {pc_ua}")
        
        # 检查移动端User-Agent
        mobile_ua = MOBILE_USER_AGENTS[0]
        if "*********" in mobile_ua or "17.2" in mobile_ua:
            print("✅ 移动端User-Agent已更新到最新版本")
        else:
            print(f"⚠️ 移动端User-Agent版本可能过旧: {mobile_ua}")
        
        return True
    except Exception as e:
        print(f"❌ User-Agent测试失败: {e}")
        return False

def test_request_config():
    """测试请求配置生成"""
    try:
        print("\n=== 测试请求配置生成 ===")

        # 导入模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("bing_script", "la_auto_10_dingding..py")
        bing_script = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(bing_script)

        get_request_config = bing_script.get_request_config
        
        # 生成PC端配置
        headers, params = get_request_config("天气预报", is_pc=True)
        
        # 检查关键参数
        required_params = ['ajaxnorecss', 'format', 'jsoncbid', 'sid']
        missing_params = []
        
        for param in required_params:
            if param in params:
                print(f"✅ 参数 {param}: {params[param]}")
            else:
                missing_params.append(param)
        
        if missing_params:
            print(f"❌ 缺少关键参数: {missing_params}")
            return False
        
        # 检查关键请求头
        required_headers = ['Sec-Ms-Gec', 'X-Client-Data', 'Sec-Fetch-Dest']
        missing_headers = []
        
        for header in required_headers:
            if header in headers:
                value = str(headers[header])
                display_value = value[:50] + "..." if len(value) > 50 else value
                print(f"✅ 请求头 {header}: {display_value}")
            else:
                missing_headers.append(header)
        
        if missing_headers:
            print(f"❌ 缺少关键请求头: {missing_headers}")
            return False
        
        print("✅ 请求配置生成正常")
        return True
        
    except Exception as e:
        print(f"❌ 请求配置测试失败: {e}")
        return False

def test_cookie_filter():
    """测试Cookie过滤功能"""
    try:
        print("\n=== 测试Cookie过滤 ===")

        # 导入模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("bing_script", "la_auto_10_dingding..py")
        bing_script = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(bing_script)

        filter_cookies = bing_script.filter_cookies
        COOKIE_CONFIG = bing_script.COOKIE_CONFIG
        
        # 模拟Cookie字符串
        test_cookie = "MUID=test; _U=test; _SS=test; _RwBf=test; SRCHHPGUSR=test; invalid=test"
        
        # 测试PC端过滤
        filtered = filter_cookies(test_cookie, 'pc')
        
        if '_RwBf=test' in filtered and 'invalid=test' not in filtered:
            print("✅ Cookie过滤功能正常")
            print(f"   过滤后: {filtered}")
        else:
            print(f"⚠️ Cookie过滤可能有问题: {filtered}")
        
        # 检查白名单扩展
        pc_whitelist = COOKIE_CONFIG['pc_whitelist']
        if '_RwBf' in pc_whitelist and 'SRCHHPGUSR' in pc_whitelist:
            print("✅ Cookie白名单已扩展")
        else:
            print("⚠️ Cookie白名单可能未完全扩展")
        
        return True
        
    except Exception as e:
        print(f"❌ Cookie过滤测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("BING积分脚本修复验证测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("User-Agent版本", test_user_agents),
        ("请求配置生成", test_request_config),
        ("Cookie过滤", test_cookie_filter)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复应该生效了。")
        print("\n建议:")
        print("1. 确保cookie.txt文件包含有效的Cookie")
        print("2. 运行脚本并观察积分增长情况")
        print("3. 关注脚本输出的测试结果")
    else:
        print("⚠️ 部分测试失败，请检查修复内容")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
